'use client';

import React from 'react';
import { Download } from 'lucide-react';
import { PDFDownloadLink } from '@react-pdf/renderer';
import { PDFExport } from '@/components/molecules/PDFExport/PDFExport';
import { Question } from '@/components/molecules/QuestionListingView/QuestionListingView';
import { ExamHeader, PrintOptions } from './types';
import { cn } from '@/utils/cn';

interface PDFDownloadButtonProps {
  questions: Question[];
  examHeader: ExamHeader;
  printOptions: PrintOptions;
  fileName: string;
  isLoading: boolean;
  isFormValid?: boolean;
  className?: string;
  onValidationError?: () => void;
}

const PDFDownloadButton: React.FC<PDFDownloadButtonProps> = ({
  questions,
  examHeader,
  printOptions,
  fileName,
  isLoading,
  isFormValid = true,
  className,
  onValidationError
}) => {
  // Error handling for PDF generation
  const [error, setError] = React.useState<Error | null>(null);

  if (error) {
    console.error('PDF generation error:', error);
    return (
      <div className={cn(
        "min-w-24 h-12 sm:h-10 bg-red-500 text-white rounded-md text-sm sm:text-base flex items-center justify-center gap-2 px-4 touch-manipulation",
        className
      )}>
        <span className="hidden sm:inline">Error loading PDF</span>
        <span className="sm:hidden">Error</span>
      </div>
    );
  }

  try {
    // Check if questions array is empty
    if (!questions || questions.length === 0) {
      return (
        <div className={cn(
          "min-w-24 h-12 sm:h-10 bg-red-500 text-white rounded-md text-sm sm:text-base flex items-center justify-center gap-2 px-4 touch-manipulation",
          className
        )}>
          <span className="hidden sm:inline">No questions to export</span>
          <span className="sm:hidden">No questions</span>
        </div>
      );
    }

    // Create the PDF document with additional error handling
    let pdfDocument;
    try {
      pdfDocument = (
        <PDFExport
          questions={questions}
          examHeader={examHeader}
          printOptions={printOptions}
        />
      );
    } catch (docError) {
      console.error('Error creating PDF document:', docError);
      return (
        <div className={cn(
          "min-w-24 h-12 sm:h-10 bg-red-500 text-white rounded-md text-sm sm:text-base flex items-center justify-center gap-2 px-4 touch-manipulation",
          className
        )}>
          <Download size={16} />
          <span className="hidden sm:inline">PDF Creation Error</span>
          <span className="sm:hidden">Error</span>
        </div>
      );
    }

    return (
      <PDFDownloadLink
        document={pdfDocument}
        fileName={fileName}
        className={cn(
          "min-w-28 sm:min-w-32 h-11 sm:h-12 bg-blue-600 hover:bg-blue-700 active:bg-blue-800 text-white transition-colors rounded-lg text-sm sm:text-base flex items-center justify-center gap-2 sm:gap-3 px-4 sm:px-6 touch-manipulation font-medium shadow-sm",
          (isLoading || !isFormValid) && "opacity-50 cursor-not-allowed",
          className
        )}
        onClick={(e) => {
          if (!isFormValid) {
            e.preventDefault();
            if (onValidationError) {
              onValidationError();
            }
          }
        }}
        aria-label="Download PDF"
      >
        {({ loading, error }) => {
          if (error) {
            console.error('PDFDownloadLink error:', error);
            return (
              <>
                <Download size={16} />
                <span className="hidden sm:inline">Error generating PDF</span>
                <span className="sm:hidden">Error</span>
              </>
            );
          }
          return (
            <>
              <Download size={16} className="sm:w-[18px] sm:h-[18px]" />
              <span className="hidden sm:inline">
                {loading ? "Generating PDF..." : "Download PDF"}
              </span>
              <span className="sm:hidden">
                {loading ? "Generating..." : "Download"}
              </span>
            </>
          );
        }}
      </PDFDownloadLink>
    );
  } catch (err) {
    console.error('PDFDownloadButton caught error:', err);

    if (err instanceof Error) {
      setError(err);
    } else {
      setError(new Error('Unknown error occurred'));
    }

    return (
      <div className={cn(
        "min-w-24 h-12 sm:h-10 bg-red-500 text-white rounded-md text-sm sm:text-base flex items-center justify-center gap-2 px-4 touch-manipulation",
        className
      )}>
        <Download size={16} />
        <span className="hidden sm:inline">Error loading PDF</span>
        <span className="sm:hidden">Error</span>
      </div>
    );
  }
};

export default PDFDownloadButton;
