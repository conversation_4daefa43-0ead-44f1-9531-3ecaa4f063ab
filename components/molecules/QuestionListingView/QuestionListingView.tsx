'use client';

import { cn } from '@/utils/cn';
import { sanitizeHtml } from '@/utils/sanitizeHtml';
import React, { useState, useEffect } from 'react';
import Icon from '@/components/atoms/Icon';
import { Button } from '@/components/atoms/Button/Button';
import { CreativeWritingRenderer } from '@/components/molecules/QuestionRenderer/CreativeWritingRenderer';
import { FillBlankContentProcessor } from '@/components/molecules/QuestionRenderer/FillBlankContentProcessor';
import { QuestionReorderModal } from '@/components/molecules/QuestionReorderModal';

// Define the type for a single question
export type Question = {
  id?: string; // Optional ID for backend operations
  type:
    | 'multiple_choice'
    | 'single_choice'
    | 'fill_blank'
    | 'creative_writing'
    | string;
  content: string;
  image?: string | null;
  svgCode?: string;
  imagePrompt?: string | null;
  options: string[];
  answer: string[];
  explain: string;
  prompt?: string; // For creative writing prompts
  subject?: string; // Subject of the question from API response
};

// Define the props type for the component
export type QuestionListingViewProps = {
  questions?: Question[];
  questionIds?: string[]; // Array of question IDs for proper ordering from API
  containerClass?: string;
  isHtmlContent?: boolean;
  worksheetInfo?: {
    topic?: string;
    subject?: string; // Added subject
    grade?: string;
    language?: string;
    level?: string;
    totalQuestions?: number;
  };
  // Reordering functionality
  allowReordering?: boolean;
  onReorderQuestion?: (fromIndex: number, toIndex: number) => void;
  isReordering?: boolean; // Optional loading state for reordering operations
  // Two-step reordering functionality
  worksheetId?: string; // Required for saving reorder changes to backend
  onSaveReorder?: (reorderedQuestions: Question[], questionIds?: string[]) => Promise<void>; // Callback to save changes with questionIds
};

const QuestionListingView: React.FC<QuestionListingViewProps> = ({
  questions,
  questionIds,
  containerClass = '',
  isHtmlContent = false,
  worksheetInfo,
  allowReordering = false,
  onReorderQuestion,
  isReordering = false,
  onSaveReorder,
}) => {

  const [localQuestions, setLocalQuestions] = useState<Question[]>(questions || []);
  const [localQuestionIds, setLocalQuestionIds] = useState<string[]>(questionIds || []);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [isReorderModalOpen, setIsReorderModalOpen] = useState(false);

  // Update local questions and questionIds when props change
  useEffect(() => {
    setLocalQuestions(questions || []);
    setLocalQuestionIds(questionIds || []);
    setHasUnsavedChanges(false);
    setSaveError(null);
  }, [questions, questionIds]);



  // Handle saving reorder changes to backend
  const handleSaveReorder = async () => {
    if (!onSaveReorder || !hasUnsavedChanges) return;

    setIsSaving(true);
    setSaveError(null);

    try {
      // Pass both questions and questionIds to the callback
      await onSaveReorder(localQuestions, localQuestionIds.length > 0 ? localQuestionIds : undefined);
      setHasUnsavedChanges(false);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to save question order';
      setSaveError(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  // Handle modal reorder save
  const handleModalReorderSave = async (reorderedQuestions: Question[], questionIds?: string[]) => {
    if (!onSaveReorder) return;

    // Update local state
    setLocalQuestions(reorderedQuestions);
    if (questionIds) {
      setLocalQuestionIds(questionIds);
    }

    // Save to backend
    await onSaveReorder(reorderedQuestions, questionIds);
  };

  return (
    <div className={cn('space-y-8', containerClass)}>
      {/* Worksheet Information - Sticky and Mobile-Optimized */}
      {worksheetInfo && (
        <div className="sticky top-0 z-50 bg-white border-b border-gray-200 shadow-sm mb-4">
          {/* Mobile Layout - Minimal and Compact */}
          <div className="block md:hidden px-3 py-2">
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center gap-2">
                {worksheetInfo.subject && (
                  <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full font-medium">
                    {worksheetInfo.subject}
                  </span>
                )}
                {worksheetInfo.grade && (
                  <span className="px-2 py-1 bg-green-100 text-green-700 rounded-full font-medium">
                    {worksheetInfo.grade}
                  </span>
                )}
              </div>
              {worksheetInfo.totalQuestions && (
                <span className="text-gray-600 font-medium">
                  {worksheetInfo.totalQuestions} Q
                </span>
              )}
            </div>
            {/* Secondary info on mobile - minimal */}
            {(worksheetInfo.topic || worksheetInfo.language || worksheetInfo.level) && (
              <div className="flex items-center gap-2 mt-1 text-xs text-gray-500 truncate">
                {worksheetInfo.topic && <span className="truncate">{worksheetInfo.topic}</span>}
                {worksheetInfo.language && <span>• {worksheetInfo.language}</span>}
                {worksheetInfo.level && <span>• {worksheetInfo.level}</span>}
              </div>
            )}
          </div>

          {/* Desktop Layout - Full Details */}
          <div className="hidden md:block p-3">
            <div className="flex flex-wrap gap-x-6 gap-y-1 text-sm">
              {worksheetInfo.subject && (
                <div className="flex items-center">
                  <span className="font-medium text-gray-600">Subject:</span>
                  <span className="ml-2 text-primary">{worksheetInfo.subject}</span>
                </div>
              )}
              {worksheetInfo.topic && (
                <div className="flex items-center">
                  <span className="font-medium text-gray-600">Topic:</span>
                  <span className="ml-2 text-primary">{worksheetInfo.topic}</span>
                </div>
              )}
              {worksheetInfo.grade && (
                <div className="flex items-center">
                  <span className="font-medium text-gray-600">Grade:</span>
                  <span className="ml-2 text-primary">{worksheetInfo.grade}</span>
                </div>
              )}
              {worksheetInfo.level && (
                <div className="flex items-center">
                  <span className="font-medium text-gray-600">Level:</span>
                  <span className="ml-2 text-primary">{worksheetInfo.level}</span>
                </div>
              )}
              {worksheetInfo.language && (
                <div className="flex items-center">
                  <span className="font-medium text-gray-600">Language:</span>
                  <span className="ml-2 text-primary">
                    {worksheetInfo.language}
                  </span>
                </div>
              )}
              {worksheetInfo.totalQuestions && (
                <div className="flex items-center">
                  <span className="font-medium text-gray-600">
                    Total Questions:
                  </span>
                  <span className="ml-2 text-primary">
                    {worksheetInfo.totalQuestions}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
      {localQuestions?.map((question, index) => {
        return (
          <div
            key={index}
            className="mb-4 md:mb-8 p-4 md:p-6 bg-white border border-gray-200 rounded-xl shadow-lg" // Mobile-optimized spacing
          >
            {/* Question Index, Type and Subject */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 md:mb-5 pb-2 md:pb-3 border-b border-gray-200">
              <div className="flex items-center gap-2 mb-2 sm:mb-0">
                <span className="text-lg md:text-xl font-semibold text-gray-700">
                  Question {index + 1}
                </span>

              </div>
              <div className="flex items-center gap-2 md:gap-3">
                {question.subject && (
                  <span className="text-xs px-2 md:px-3 py-1 bg-blue-100 text-blue-700 rounded-full font-medium">
                    {question.subject}
                  </span>
                )}
                <span className="text-xs px-2 md:px-3 py-1 bg-green-100 text-green-700 rounded-full font-medium capitalize">
                  {question.type.replace('_', ' ')}
                </span>
              </div>
            </div>

            {/* Question Content */}
            <div className="bg-gray-50 p-3 md:p-5 rounded-lg mb-3 md:mb-5">
              {question.type === 'fill_blank' ? (
                <FillBlankContentProcessor
                  content={question.content}
                  answers={question.answer}
                  isHtmlContent={isHtmlContent}
                />
              ) : isHtmlContent ? (
                <div // Changed h2 to div for better semantic if content is complex HTML
                  className="text-sm md:text-base text-gray-800 leading-relaxed question-content-html" // Mobile-optimized font size
                  dangerouslySetInnerHTML={{ __html: question.content }}
                />
              ) : (
                <p className="text-sm md:text-base text-gray-800 leading-relaxed">{question.content}</p> // Mobile-optimized font size
              )}
            </div>

            {/* SVG Illustration or Image (if available) */}
            {question?.image && (
              <div
                className="my-4 w-full max-w-lg"
                dangerouslySetInnerHTML={{ __html: question.image }}
              />
            )}

            {/* Question Type Specific Rendering */}
            {/* Multiple Choice & Single Choice Options */}
            {(question.type === 'multiple_choice' ||
              question.type === 'single_choice') &&
              question?.options?.length > 0 && (
                <div className="space-y-1 mt-3 md:mt-5">
                  {question?.options?.map((option, optionIndex) => {
                    const isAnswer = question.answer?.includes(option);
                    const isSingleChoice = question.type === 'single_choice';
                    const inputType = isSingleChoice ? 'radio' : 'checkbox';

                    return (
                      <label
                        key={optionIndex}
                        className="flex items-start space-x-2 md:space-x-3 cursor-pointer py-1.5 md:py-3 border border-gray-100 hover:bg-gray-50 rounded-md px-2 md:px-3 mb-1.5 md:mb-2"
                      >
                        <input
                          type={inputType}
                          name={`question-${index}`}
                          value={option}
                          defaultChecked={isAnswer}
                          disabled={!isAnswer}
                          className={cn(
                            inputType === 'radio'
                              ? 'radio radio-primary radio-sm md:radio-md'
                              : 'checkbox checkbox-primary checkbox-sm md:checkbox-md',
                            'mt-0.5 md:mt-1 border-2',
                            isAnswer ? 'border-primary' : 'border-gray-300'
                          )}
                        />
                        {isHtmlContent ? (
                          <span
                            className={cn(
                              'flex-1 pt-0 md:pt-0.5 text-sm md:text-base leading-snug md:leading-relaxed',
                              isAnswer && 'font-medium text-primary'
                            )}
                            dangerouslySetInnerHTML={{ __html: option }}
                          />
                        ) : (
                          <span
                            className={cn(
                              'flex-1 pt-0 md:pt-0.5 text-sm md:text-base leading-snug md:leading-relaxed',
                              isAnswer && 'font-medium text-primary'
                            )}
                          >
                            {option}
                          </span>
                        )}
                      </label>
                    );
                  })}
                </div>
              )}

              {/* Creative Writing */}
              {question.type === 'creative_writing' && (
                <CreativeWritingRenderer
                  answer={question.answer}
                  prompt={question.prompt}
                  isHtmlContent={isHtmlContent}
                  minWords={50}
                  maxWords={300}
                />
              )}

            {/* Explanation Accordion */}
            {question?.explain && (
              <ExplanationAccordion
                explanation={question.explain}
                isHtmlContent={isHtmlContent}
              />
            )}
          </div>
        );
      })}



      {/* Update Order Button - Show when there are unsaved changes */}
      {allowReordering && hasUnsavedChanges && onSaveReorder && (
        <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
            <div className="flex items-center gap-2">
              <Icon variant="help-circle" size={4} className="text-blue-600" />
              <div>
                <p className="text-sm font-medium text-blue-800">
                  You have unsaved question order changes
                </p>
                <p className="text-xs text-blue-600">
                  Click &quot;Update Order&quot; to save your changes to the server
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setLocalQuestions(questions || []);
                  setLocalQuestionIds(questionIds || []);
                  setHasUnsavedChanges(false);
                  setSaveError(null);
                }}
                disabled={isSaving}
                className="text-gray-600 border-gray-300 hover:bg-gray-50 px-3 py-2 text-sm"
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleSaveReorder}
                disabled={isSaving}
                className="min-w-[100px] px-4 py-2 text-sm"
              >
                {isSaving ? (
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Saving...
                  </div>
                ) : (
                  'Update Order'
                )}
              </Button>
            </div>
          </div>
          {saveError && (
            <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
              <div className="flex items-center gap-2">
                <Icon variant="alert-triangle" size={3.5} className="text-red-600" />
                {saveError}
              </div>
            </div>
          )}
        </div>
      )}



      {/* Question Reorder Modal */}
      <QuestionReorderModal
        isOpen={isReorderModalOpen}
        onClose={() => setIsReorderModalOpen(false)}
        questions={localQuestions}
        questionIds={localQuestionIds}
        onSaveReorder={handleModalReorderSave}
        isLoading={isSaving}
      />
    </div>
  );
};

// Explanation Accordion Component
type ExplanationAccordionProps = {
  explanation: string;
  isHtmlContent?: boolean;
};

const ExplanationAccordion: React.FC<ExplanationAccordionProps> = ({
  explanation,
  isHtmlContent = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="mt-3 md:mt-5 border border-gray-200 rounded-md overflow-hidden shadow-sm">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between py-2 md:py-2.5 px-3 md:px-4 bg-gray-50 focus:outline-none transition-colors hover:bg-gray-100"
      >
        <div className="flex items-center gap-2">
          <Icon
            variant="chevron-down"
            size={3.5}
            className={cn(
              'transition-transform duration-200 text-primary',
              isOpen && 'rotate-180'
            )}
          />
          <span className="font-medium text-primary text-xs md:text-sm">
            View Explanation
          </span>
        </div>
      </button>

      <div
        className={cn(
          'transition-all duration-300 ease-in-out overflow-hidden',
          isOpen ? 'max-h-[1000px] opacity-100' : 'max-h-0 opacity-0'
        )}
      >
        <div className="p-3 md:p-4 bg-white border-t border-gray-200">
          {isHtmlContent ? (
            <div
              className="text-gray-700 text-xs md:text-sm leading-relaxed max-w-none"
              dangerouslySetInnerHTML={{
                __html: sanitizeHtml(explanation),
              }}
            />
          ) : (
            <div className="text-gray-700 text-xs md:text-sm leading-relaxed">
              {explanation.split('\n').map((paragraph, i) =>
                paragraph.trim() ? (
                  <p key={i} className="mb-2 md:mb-3">
                    {paragraph}
                  </p>
                ) : null
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default QuestionListingView;
